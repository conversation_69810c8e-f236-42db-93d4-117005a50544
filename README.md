# Flask Hello World IA Docker Application

A simple Flask web application that displays "Hello World IA" and runs in a Docker container.

## Files Structure

- `app.py` - Main Flask application
- `requirements.txt` - Python dependencies
- `Dockerfile` - Docker configuration
- `README.md` - This file

## Building and Running the Docker Container

### 1. Build the Docker Image

```bash
docker build -t flask-hello-world-ia .
```

### 2. Run the Docker Container

```bash
docker run -p 8000:8000 flask-hello-world-ia
```

### 3. Access the Application

Open your web browser and navigate to:
```
http://localhost:8000
```

You should see "Hello World IA" displayed on the page.

## Alternative Docker Commands

### Run in detached mode (background)
```bash
docker run -d -p 8000:8000 --name flask-app flask-hello-world-ia
```

### Stop the container (if running in detached mode)
```bash
docker stop flask-app
```

### Remove the container
```bash
docker rm flask-app
```

### View running containers
```bash
docker ps
```

## Development

To run the Flask application directly (without Docker):

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   python app.py
   ```

The application will be available at `http://localhost:8000`.
